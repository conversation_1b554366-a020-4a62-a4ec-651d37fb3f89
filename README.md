# 空间插值系统 v1.0

基于Python的降雨空间插值系统，支持PRISM、OI、Kriging、IDW四种插值方法，使用Delaunay三角网和留一法交叉验证。

## 功能特点

- **四种插值方法**: PRISM、OI (最优插值)、Kriging、IDW (反距离权重)
- **Delaunay三角网**: 用于空间融合和邻近站点选择
- **留一法验证**: 交叉验证评估插值精度
- **并行处理**: 支持多核并行计算，提高处理效率
- **批量处理**: 自动处理所有洪水场次
- **评价指标**: MAE、RMSE、R²、NSE等多种评价指标
- **内存优化**: 分批处理，避免内存溢出
- **结果汇总**: 自动生成方法比较和详细结果

## 系统要求

- Python 3.7+
- 依赖包: numpy, pandas, scipy, rasterio, tqdm, matplotlib, seaborn

## 安装依赖

```bash
pip install numpy pandas scipy rasterio tqdm matplotlib seaborn
```

## 文件结构

```
D:/pythondate/spatial_interpolation/
├── input_another/           # 输入数据目录
│   ├── 2009-1/             # 洪水场次文件夹
│   │   ├── 80606500.csv    # 站点降雨数据
│   │   └── ...
│   └── 2023-1/
├── terrain/90/             # 地形数据
│   ├── dem.asc             # 数字高程模型
│   ├── slope.asc           # 坡度
│   └── aspect.asc          # 坡向
├── stations.csv            # 站点信息 (站点,经度,纬度)
├── output/                 # 输出结果目录
│   ├── PRISM/              # PRISM方法结果
│   ├── OI/                 # OI方法结果
│   ├── Kriging/            # Kriging方法结果
│   └── IDW/                # IDW方法结果
├── PRISM_python/           # PRISM方法代码
├── OI_python/              # OI方法代码
├── Kriging_python/         # Kriging方法代码
├── IDW_python/             # IDW方法代码
├── config.py               # 主配置文件
├── utils.py                # 工具模块
├── main_controller.py      # 主控制器
└── run_interpolation.py    # 简化运行脚本
```

## 使用方法

### 方法1: 简化运行 (推荐新手)

直接运行简化脚本，按提示操作：

```bash
python run_interpolation.py
```

程序会引导您：
1. 选择插值方法
2. 选择洪水场次
3. 设置并行进程数
4. 确认配置并运行

### 方法2: 命令行运行

使用主控制器，支持更多参数：

```bash
# 运行所有方法处理所有场次
python main_controller.py

# 只运行PRISM方法
python main_controller.py --methods PRISM

# 运行多个方法
python main_controller.py --methods PRISM Kriging

# 指定洪水场次
python main_controller.py --events 2009-1 2010-1

# 设置并行进程数（新策略：点插值串行 + 栅格插值并行）
python main_controller.py --num_processes 1 --grid_parallel_chunks 6

# 生成栅格输出（推荐配置）
python main_controller.py --generate_grids --num_processes 1 --grid_parallel_chunks 6

# 创建可视化图表
python main_controller.py --create_plots
```

### 方法3: 单独运行各方法

进入各方法文件夹运行：

```bash
# PRISM方法
cd PRISM_python
python config_prism.py

# OI方法
cd OI_python
python config_oi.py

# Kriging方法
cd Kriging_python
python config_kriging.py

# IDW方法
cd IDW_python
python config_idw.py
```

## 配置参数

### 主要配置 (config.py)

```python
# 基础路径
base_dir = "D:/pythondate/spatial_interpolation"
input_base_dir = "input_another"
terrain_dir = "terrain/90"
output_base_dir = "output"

# 处理参数（新并行策略）
num_processes = 1           # 点插值进程数（推荐1，避免嵌套并行）
grid_parallel_chunks = 6    # 栅格插值并行进程数
max_neighbors = 10          # 最大邻居数量
generate_grids = True       # 是否生成栅格输出

# 评价指标阈值
nse_threshold = -10.0       # NSE系数阈值
```

### PRISM参数

```python
prism_elev_weight = 0.7     # 高程权重
prism_slope_weight = 0.2    # 坡度权重
prism_aspect_weight = 0.1   # 坡向权重
prism_distance_power = 2.0  # 距离权重指数
```

### Kriging参数

```python
kriging_type = "ordinary"           # ordinary, simple, universal
variogram_model = "spherical"       # spherical, exponential, gaussian, linear
```

### IDW参数

```python
idw_power = 2.0                     # 距离权重指数
idw_search_radius = None            # 搜索半径，None表示使用所有点
```

### OI参数

```python
oi_obs_error = 0.01                 # 观测误差
oi_background_error = 1.0           # 背景误差
```

## 输出结果

### 主要输出文件

1. **methods_comparison.csv**: 各方法性能比较汇总
2. **all_results_detailed.csv**: 所有方法详细结果
3. **[方法名]_summary.csv**: 各方法汇总结果
4. **output/[方法名]/[场次]/**: 各场次详细结果
   - **points/**: 站点插值结果
   - **metrics_*.txt**: 评价指标文件

### 评价指标说明

- **MAE**: 平均绝对误差，越小越好
- **RMSE**: 均方根误差，越小越好
- **R²**: 决定系数，越接近1越好
- **NSE**: 纳什效率系数，>0.75表示模型效果很好

## 数据格式要求

### 站点文件 (stations.csv)

```csv
站点,经度,纬度
80606500,113.2345,23.1234
80606501,113.2456,23.1345
```

### 降雨数据文件 (如80606500.csv)

```csv
时间,雨量
2009-01-01 01:00:00,0.5
2009-01-01 02:00:00,1.2
2009-01-01 03:00:00,0.0
```

### 地形数据

- **dem.asc**: 数字高程模型
- **slope.asc**: 坡度数据
- **aspect.asc**: 坡向数据

所有地形数据应为ASC格式的栅格文件。

## 注意事项

1. **内存管理**: 系统已优化内存使用，大数据集建议分批处理
2. **零值处理**: 系统正确处理降雨量为0的情况，0不被视为异常值
3. **新并行策略**:
   - 点插值使用串行处理（num_processes=1）
   - 栅格插值使用并行处理（grid_parallel_chunks=6）
   - 避免嵌套并行问题，显著提升性能
4. **路径设置**: 确保所有路径正确，特别是中文路径可能需要特殊处理
5. **编码格式**: 支持UTF-8和GBK编码的CSV文件
6. **栅格插值性能**: 新并行策略下，栅格插值速度提升6倍以上

## 故障排除

### 常见问题

1. **路径不存在**: 检查config.py中的路径设置
2. **编码错误**: 确保CSV文件编码为UTF-8或GBK
3. **内存不足**: 减少并行进程数或分批处理
4. **依赖包缺失**: 使用pip安装所需依赖包

### 日志文件

系统会在output/logs/目录下生成详细日志文件，可用于问题诊断。

## 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 数据格式是否正确
3. 路径配置是否正确
4. 依赖包是否完整安装

## 版本历史

- v1.0: 初始版本，支持四种插值方法和完整的验证评估体系
