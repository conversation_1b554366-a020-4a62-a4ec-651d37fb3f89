# -*- coding: utf-8 -*-
"""
演示README.md中的使用方法
展示如何正确使用空间插值系统
"""

import os
import sys
import subprocess

def show_usage_examples():
    """显示使用示例"""
    print("📖 README.md 使用方法演示")
    print("=" * 60)
    
    examples = [
        {
            "title": "1. 交互式运行（推荐新手）",
            "command": "python run_interpolation.py",
            "description": "启动交互式界面，引导选择参数"
        },
        {
            "title": "2. 运行所有方法（新并行策略）",
            "command": "python main_controller.py --methods all --generate_grids --num_processes 1 --grid_parallel_chunks 6",
            "description": "使用优化的并行策略运行所有插值方法"
        },
        {
            "title": "3. 只运行OI方法",
            "command": "python main_controller.py --methods OI --generate_grids",
            "description": "只运行OI插值方法并生成栅格"
        },
        {
            "title": "4. 运行多个方法",
            "command": "python main_controller.py --methods OI Kriging --generate_grids",
            "description": "运行OI和Kriging两种方法"
        },
        {
            "title": "5. 处理特定洪水场次",
            "command": "python main_controller.py --methods PRISM --events 2020-1 2020-2 --generate_grids",
            "description": "只处理指定的洪水场次"
        },
        {
            "title": "6. 高性能OI插值",
            "command": "python run_oi_optimized.py",
            "description": "使用专门优化的OI插值脚本"
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}")
        print("-" * 40)
        print(f"命令: {example['command']}")
        print(f"说明: {example['description']}")
    
    print(f"\n💡 重要提示:")
    print("- 新并行策略: 点插值串行(1进程) + 栅格插值并行(6进程)")
    print("- 避免嵌套并行问题，性能提升6倍以上")
    print("- 推荐使用 --generate_grids 生成完整的栅格结果")

def run_demo():
    """运行演示"""
    print(f"\n🚀 演示运行")
    print("=" * 40)
    
    # 检查系统状态
    print("1. 检查系统状态...")
    try:
        from config import InterpolationConfig
        config = InterpolationConfig()
        
        print(f"✅ 配置加载成功")
        print(f"   洪水事件: {len(config.flood_events)} 个")
        print(f"   并行配置: 点插值{config.num_processes}进程 + 栅格插值{config.grid_parallel_chunks}进程")
        
        # 验证路径
        paths = config.validate_paths()
        missing_paths = [path for path, exists in paths.items() if not exists]
        
        if missing_paths:
            print(f"⚠️  缺少路径: {len(missing_paths)} 个")
            return False
        else:
            print(f"✅ 所有路径验证通过")
            
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
        return False
    
    # 询问是否运行演示
    print(f"\n2. 选择演示模式:")
    print("a) 只显示命令（不实际运行）")
    print("b) 运行交互式脚本")
    print("c) 运行简单测试（OI方法，1个事件）")
    
    while True:
        try:
            choice = input("\n请选择 (a/b/c): ").strip().lower()
            
            if choice == 'a':
                print(f"\n📋 所有可用命令已显示在上方")
                print(f"💡 您可以复制粘贴这些命令来运行")
                return True
                
            elif choice == 'b':
                print(f"\n🚀 启动交互式脚本...")
                print(f"命令: python run_interpolation.py")
                print(f"💡 这将启动交互式界面，请按提示操作")
                
                # 实际运行交互式脚本
                try:
                    subprocess.run([sys.executable, "run_interpolation.py"], check=True)
                    return True
                except subprocess.CalledProcessError as e:
                    print(f"❌ 运行失败: {e}")
                    return False
                except KeyboardInterrupt:
                    print(f"\n👋 用户中断")
                    return True
                    
            elif choice == 'c':
                print(f"\n🧪 运行简单测试...")
                print(f"命令: python main_controller.py --methods OI --events {config.flood_events[0]} --generate_grids")
                
                # 运行简单测试
                try:
                    cmd = [
                        sys.executable, "main_controller.py",
                        "--methods", "OI",
                        "--events", config.flood_events[0],
                        "--generate_grids",
                        "--num_processes", "1",
                        "--grid_parallel_chunks", "4"  # 使用较少进程进行测试
                    ]
                    
                    print(f"⚡ 开始运行测试...")
                    subprocess.run(cmd, check=True)
                    print(f"✅ 测试完成!")
                    return True
                    
                except subprocess.CalledProcessError as e:
                    print(f"❌ 测试失败: {e}")
                    return False
                except KeyboardInterrupt:
                    print(f"\n👋 用户中断测试")
                    return True
            else:
                print("❌ 无效选择，请输入 a、b 或 c")
                
        except KeyboardInterrupt:
            print(f"\n👋 用户取消")
            return True

def main():
    """主函数"""
    try:
        # 显示使用示例
        show_usage_examples()
        
        # 运行演示
        success = run_demo()
        
        if success:
            print(f"\n🎉 演示完成!")
            print(f"📖 更多详细信息请查看 README.md")
            print(f"💡 建议从交互式运行开始: python run_interpolation.py")
        else:
            print(f"\n⚠️  演示遇到问题")
            print(f"💡 请检查系统配置和依赖")
            
    except Exception as e:
        print(f"\n💥 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("启动README.md使用方法演示...")
    main()
