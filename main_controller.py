# -*- coding: utf-8 -*-
"""
空间插值系统主控制器
统一管理所有插值方法的执行和结果汇总
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime
import argparse
from typing import Dict, List
import matplotlib.pyplot as plt
import seaborn as sns

# 导入配置和工具
from config import InterpolationConfig, default_config
from utils import setup_logging

# 导入各插值方法
from PRISM_python.prism_interpolation import PRISMProcessor
from OI_python.oi_interpolation import OIProcessor
from Kriging_python.kriging_interpolation import KrigingProcessor
from IDW_python.idw_interpolation import IDWProcessor

logger = logging.getLogger(__name__)

class MainController:
    """空间插值主控制器"""
    
    def __init__(self, config: InterpolationConfig):
        self.config = config
        self.processors = {
            'PRISM': PRISMProcessor(config),
            'OI': OIProcessor(config),
            'Kriging': KrigingProcessor(config),
            'IDW': IDWProcessor(config)
        }
        self.results = {}
    
    def run_single_method(self, method: str) -> pd.DataFrame:
        """运行单个插值方法"""
        if method not in self.processors:
            logger.error(f"未知的插值方法: {method}")
            return pd.DataFrame()
        
        logger.info(f"开始运行 {method} 插值方法...")
        
        try:
            processor = self.processors[method]
            summary = processor.process_all_events()
            
            if not summary.empty:
                self.results[method] = summary
                logger.info(f"{method} 插值方法完成，处理了 {len(summary)} 个洪水场次")
            else:
                logger.warning(f"{method} 插值方法没有生成有效结果")
            
            return summary
            
        except Exception as e:
            logger.error(f"运行 {method} 插值方法失败: {e}")
            return pd.DataFrame()
    
    def run_all_methods(self) -> Dict[str, pd.DataFrame]:
        """运行所有插值方法"""
        logger.info("开始运行所有插值方法...")
        
        all_results = {}
        
        for method in self.config.methods:
            logger.info(f"\n{'='*60}")
            logger.info(f"正在处理: {method}")
            logger.info(f"{'='*60}")
            
            summary = self.run_single_method(method)
            if not summary.empty:
                all_results[method] = summary
        
        self.results = all_results
        return all_results

    def run_all(self) -> bool:
        """运行所有方法并返回成功状态"""
        try:
            results = self.run_all_methods()
            if results:
                # 创建比较汇总
                self.create_comparison_summary()
                # 打印汇总
                self.print_summary()
                return True
            return False
        except Exception as e:
            logger.error(f"运行失败: {e}")
            return False
    
    def create_comparison_summary(self) -> pd.DataFrame:
        """创建方法比较汇总"""
        if not self.results:
            logger.warning("没有结果可供比较")
            return pd.DataFrame()
        
        logger.info("创建方法比较汇总...")
        
        # 合并所有方法的结果
        all_data = []
        for method, summary in self.results.items():
            if not summary.empty:
                summary_copy = summary.copy()
                summary_copy['method'] = method
                all_data.append(summary_copy)
        
        if not all_data:
            return pd.DataFrame()
        
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 计算每个方法的统计指标
        method_stats = []
        for method in self.config.methods:
            method_data = combined_df[combined_df['method'] == method]
            if not method_data.empty:
                stats = {
                    'method': method,
                    'events_count': len(method_data),
                    'mean_MAE': method_data['MAE'].mean(),
                    'mean_RMSE': method_data['RMSE'].mean(),
                    'mean_R2': method_data['R2'].mean(),
                    'mean_NSE': method_data['NSE'].mean(),
                    'std_MAE': method_data['MAE'].std(),
                    'std_RMSE': method_data['RMSE'].std(),
                    'std_R2': method_data['R2'].std(),
                    'std_NSE': method_data['NSE'].std(),
                    'total_samples': method_data['count'].sum()
                }
                method_stats.append(stats)
        
        comparison_df = pd.DataFrame(method_stats)
        
        # 保存比较结果
        output_file = os.path.join(self.config.base_dir, "output", "methods_comparison.csv")
        comparison_df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"方法比较结果已保存到: {output_file}")
        
        # 保存详细结果
        detailed_file = os.path.join(self.config.base_dir, "output", "all_results_detailed.csv")
        combined_df.to_csv(detailed_file, index=False, encoding='utf-8')
        logger.info(f"详细结果已保存到: {detailed_file}")
        
        return comparison_df
    
    def create_visualization(self):
        """创建可视化图表"""
        if not self.results:
            logger.warning("没有结果可供可视化")
            return
        
        logger.info("创建可视化图表...")
        
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 合并数据
            all_data = []
            for method, summary in self.results.items():
                if not summary.empty:
                    summary_copy = summary.copy()
                    summary_copy['method'] = method
                    all_data.append(summary_copy)
            
            if not all_data:
                return
            
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 创建子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('空间插值方法性能比较', fontsize=16, fontweight='bold')
            
            # MAE比较
            sns.boxplot(data=combined_df, x='method', y='MAE', ax=axes[0, 0])
            axes[0, 0].set_title('平均绝对误差 (MAE)')
            axes[0, 0].set_ylabel('MAE (mm)')
            
            # RMSE比较
            sns.boxplot(data=combined_df, x='method', y='RMSE', ax=axes[0, 1])
            axes[0, 1].set_title('均方根误差 (RMSE)')
            axes[0, 1].set_ylabel('RMSE (mm)')
            
            # R2比较
            sns.boxplot(data=combined_df, x='method', y='R2', ax=axes[1, 0])
            axes[1, 0].set_title('决定系数 (R²)')
            axes[1, 0].set_ylabel('R²')
            
            # NSE比较
            sns.boxplot(data=combined_df, x='method', y='NSE', ax=axes[1, 1])
            axes[1, 1].set_title('纳什效率系数 (NSE)')
            axes[1, 1].set_ylabel('NSE')
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = os.path.join(self.config.base_dir, "output", "methods_comparison.png")
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"可视化图表已保存到: {plot_file}")
            
        except Exception as e:
            logger.error(f"创建可视化图表失败: {e}")
    
    def print_summary(self):
        """打印汇总信息"""
        if not self.results:
            print("没有可用的结果")
            return
        
        print("\n" + "="*80)
        print("空间插值系统运行汇总")
        print("="*80)
        
        for method, summary in self.results.items():
            if not summary.empty:
                print(f"\n{method} 方法:")
                print(f"  处理洪水场次: {len(summary)}")
                print(f"  平均 MAE: {summary['MAE'].mean():.4f} ± {summary['MAE'].std():.4f} mm")
                print(f"  平均 RMSE: {summary['RMSE'].mean():.4f} ± {summary['RMSE'].std():.4f} mm")
                print(f"  平均 R²: {summary['R2'].mean():.4f} ± {summary['R2'].std():.4f}")
                print(f"  平均 NSE: {summary['NSE'].mean():.4f} ± {summary['NSE'].std():.4f}")
                print(f"  总样本数: {summary['count'].sum()}")
        
        print("\n" + "="*80)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='空间插值系统主控制器')
    
    parser.add_argument('--methods', nargs='+', 
                       choices=['PRISM', 'OI', 'Kriging', 'IDW', 'all'],
                       default=['all'],
                       help='要运行的插值方法')
    
    parser.add_argument('--events', nargs='+',
                       help='要处理的洪水场次，默认处理所有')
    
    parser.add_argument('--num_processes', type=int, default=1,
                       help='点插值并行进程数（推荐1，避免嵌套并行）')

    parser.add_argument('--grid_parallel_chunks', type=int, default=6,
                       help='栅格插值并行进程数')
    
    parser.add_argument('--generate_grids', action='store_true',
                       help='是否生成栅格输出')
    
    parser.add_argument('--create_plots', action='store_true',
                       help='是否创建可视化图表')
    
    parser.add_argument('--config_file', type=str,
                       help='配置文件路径')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    if args.config_file and os.path.exists(args.config_file):
        # 这里可以实现从文件加载配置的逻辑
        config = default_config
    else:
        config = default_config
    
    # 更新配置
    if args.events:
        config.flood_events = args.events
    
    config.num_processes = args.num_processes
    config.grid_parallel_chunks = args.grid_parallel_chunks
    config.generate_grids = args.generate_grids
    
    if 'all' in args.methods:
        config.methods = ['PRISM', 'OI', 'Kriging', 'IDW']
    else:
        config.methods = args.methods
    
    # 设置日志
    output_dir = os.path.join(config.base_dir, "output")
    os.makedirs(output_dir, exist_ok=True)
    setup_logging(output_dir, "main")
    
    # 打印配置信息
    config.print_config()
    
    # 验证路径
    paths = config.validate_paths()
    missing_paths = [path for path, exists in paths.items() if not exists]
    if missing_paths:
        logger.error(f"以下路径不存在: {missing_paths}")
        return
    
    # 创建控制器并运行
    controller = MainController(config)
    
    try:
        # 运行插值方法
        results = controller.run_all_methods()
        
        if results:
            # 创建比较汇总
            controller.create_comparison_summary()

            # 创建可视化
            if args.create_plots:
                controller.create_visualization()

            # 打印汇总
            controller.print_summary()

            logger.info("所有插值方法运行完成！")
            
        else:
            logger.error("没有生成任何有效结果")
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}", exc_info=True)

if __name__ == "__main__":
    main()
