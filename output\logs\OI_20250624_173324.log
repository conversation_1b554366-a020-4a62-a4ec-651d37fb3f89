2025-06-24 17:33:24,361 - INFO - 开始处理所有洪水场次...
2025-06-24 17:33:24,361 - INFO - 开始处理洪水场次: 2009-1
2025-06-24 17:33:24,365 - INFO - 成功加载 34 个站点信息
2025-06-24 17:33:24,365 - INFO - 找到 14 个降雨数据文件
2025-06-24 17:33:24,731 - INFO - 成功加载了 14 个站点的降雨数据
2025-06-24 17:33:24,761 - INFO - 初始化OI插值器...
2025-06-24 17:33:24,768 - INFO - 计算协方差矩阵...
2025-06-24 17:33:24,813 - INFO - 协方差矩阵计算完成
2025-06-24 17:33:24,813 - INFO - OI插值器初始化完成
2025-06-24 17:33:24,815 - INFO - 共有 384 个时间步需要处理
2025-06-24 17:33:24,816 - INFO - 使用串行处理（为栅格插值并行优化）...
2025-06-24 17:33:32,055 - INFO - OI插值完成 - 2009-1: MAE=0.1532, RMSE=0.7822, R2=0.7799, NSE=0.7799
2025-06-24 17:33:32,080 - INFO - 开始生成OI栅格插值结果...
2025-06-24 17:33:32,081 - INFO - 初始化栅格模板: D:/pythondata/pythondate\terrain/90\dem.asc
2025-06-24 17:33:32,731 - INFO - 已加载掩膜文件: D:/pythondata/pythondate\terrain/90\mask.asc
2025-06-24 17:33:32,732 - INFO - 栅格模板初始化完成: 962x800 像素
2025-06-24 17:33:32,734 - INFO - 处理 384 个时间步的栅格插值
2025-06-24 17:33:32,745 - INFO - 有效时间步: 384/384
2025-06-24 17:33:32,747 - INFO - 处理时间步 1/384: 2009-04-16 03:00:00 (14个站点)
2025-06-24 17:33:32,747 - INFO - 开始栅格插值: 2009-04-16 03:00:00 (站点数: 14)
2025-06-24 17:33:32,748 - INFO - 创建栅格分块: 2x2 = 4 个块
2025-06-24 17:33:32,748 - INFO - 创建了 4 个栅格块
