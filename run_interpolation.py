# -*- coding: utf-8 -*-
"""
空间插值系统简化运行脚本
新手友好的配置和运行界面
"""

import os
import sys
from datetime import datetime

# 导入主要模块
from config import InterpolationConfig
from main_controller import MainController
from utils import setup_logging

def print_banner():
    """打印程序横幅"""
    print("="*80)
    print("                    空间插值系统 v1.0")
    print("          支持 PRISM、OI、Kriging、IDW 四种插值方法")
    print("="*80)
    print()

def get_user_config():
    """获取用户配置"""
    print("请配置运行参数:")
    print()
    
    # 创建默认配置
    config = InterpolationConfig()
    
    # 选择插值方法
    print("1. 选择插值方法:")
    print("   1) PRISM")
    print("   2) OI (最优插值)")
    print("   3) Kriging")
    print("   4) IDW (反距离权重)")
    print("   5) 全部方法")
    
    while True:
        choice = input("请选择 (1-5, 默认5): ").strip()
        if not choice:
            choice = "5"
        
        if choice == "1":
            config.methods = ["PRISM"]
            break
        elif choice == "2":
            config.methods = ["OI"]
            break
        elif choice == "3":
            config.methods = ["Kriging"]
            break
        elif choice == "4":
            config.methods = ["IDW"]
            break
        elif choice == "5":
            config.methods = ["PRISM", "OI", "Kriging", "IDW"]
            break
        else:
            print("无效选择，请重新输入")
    
    print(f"已选择方法: {', '.join(config.methods)}")
    print()
    
    # 选择洪水场次
    print("2. 选择洪水场次:")
    print(f"   检测到 {len(config.flood_events)} 个洪水场次:")
    for i, event in enumerate(config.flood_events[:10], 1):
        print(f"   {i}) {event}")
    if len(config.flood_events) > 10:
        print(f"   ... 还有 {len(config.flood_events) - 10} 个场次")
    
    print("   选项:")
    print("   1) 处理所有场次")
    print("   2) 处理前5个场次 (测试)")
    print("   3) 自定义选择")
    
    while True:
        choice = input("请选择 (1-3, 默认1): ").strip()
        if not choice:
            choice = "1"
        
        if choice == "1":
            # 处理所有场次
            break
        elif choice == "2":
            # 处理前5个场次
            config.flood_events = config.flood_events[:5]
            break
        elif choice == "3":
            # 自定义选择
            print("请输入要处理的场次编号 (用空格分隔):")
            indices = input().strip().split()
            try:
                selected_events = [config.flood_events[int(i)-1] for i in indices 
                                 if 1 <= int(i) <= len(config.flood_events)]
                if selected_events:
                    config.flood_events = selected_events
                    break
                else:
                    print("无效选择，使用所有场次")
                    break
            except:
                print("输入格式错误，使用所有场次")
                break
        else:
            print("无效选择，请重新输入")
    
    print(f"将处理 {len(config.flood_events)} 个洪水场次")
    print()
    
    # 并行设置 (新策略：点插值串行 + 栅格插值并行)
    print("3. 并行处理设置:")
    print("   新策略: 点插值串行 + 栅格插值并行")
    while True:
        try:
            grid_processes = input(f"栅格插值并行进程数 (1-{os.cpu_count()}, 默认6): ").strip()
            if not grid_processes:
                config.grid_parallel_chunks = min(6, os.cpu_count())
                break

            grid_processes = int(grid_processes)
            if 1 <= grid_processes <= os.cpu_count():
                config.grid_parallel_chunks = grid_processes
                break
            else:
                print(f"进程数应在 1-{os.cpu_count()} 之间")
        except ValueError:
            print("请输入有效数字")

    # 强制点插值为串行
    config.num_processes = 1
    print(f"点插值: 串行处理 (1进程)")
    print(f"栅格插值: 并行处理 ({config.grid_parallel_chunks}进程)")
    print()
    
    # 输出设置
    print("4. 输出设置:")
    generate_grids = input("是否生成栅格文件? (y/N, 默认N): ").strip().lower()
    config.generate_grids = generate_grids in ['y', 'yes', '是']
    
    print(f"栅格输出: {'开启' if config.generate_grids else '关闭'}")
    print()
    
    return config

def confirm_run(config):
    """确认运行配置"""
    print("运行配置确认:")
    print("-" * 40)
    print(f"插值方法: {', '.join(config.methods)}")
    print(f"洪水场次: {len(config.flood_events)} 个")
    print(f"点插值: 串行处理 ({config.num_processes}进程)")
    print(f"栅格插值: 并行处理 ({config.grid_parallel_chunks}进程)")
    print(f"栅格输出: {'开启' if config.generate_grids else '关闭'}")
    print(f"基础目录: {config.base_dir}")
    print("-" * 40)
    
    while True:
        confirm = input("确认开始运行? (Y/n): ").strip().lower()
        if not confirm or confirm in ['y', 'yes', '是']:
            return True
        elif confirm in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")

def run_with_progress(config):
    """运行插值并显示进度"""
    print("\n开始运行空间插值...")
    print("="*60)
    
    # 设置日志
    output_dir = os.path.join(config.base_dir, "output")
    os.makedirs(output_dir, exist_ok=True)
    setup_logging(output_dir, "run")
    
    # 创建控制器
    controller = MainController(config)

    try:
        start_time = datetime.now()

        # 运行所有方法
        results = controller.run_all()

        end_time = datetime.now()
        duration = end_time - start_time

        if results:
            print("\n" + "="*60)
            print("运行完成!")
            print(f"总耗时: {duration}")
            print("="*60)
            
            print(f"\n结果文件保存在: {output_dir}")
            print("主要输出文件:")
            print("  - methods_comparison.csv: 方法比较汇总")
            print("  - all_results_detailed.csv: 详细结果")
            print("  - [方法名]_summary.csv: 各方法汇总")
            print("  - output/[方法名]/[场次]/: 详细插值结果")
            
        else:
            print("运行失败，没有生成有效结果")
            return False
            
    except Exception as e:
        print(f"运行出错: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    try:
        # 获取用户配置
        config = get_user_config()
        
        # 验证路径
        paths = config.validate_paths()
        missing_paths = [path for path, exists in paths.items() if not exists]
        
        if missing_paths:
            print("错误: 以下必要路径不存在:")
            for path in missing_paths:
                print(f"  - {path}")
            print("\n请检查配置文件中的路径设置")
            return
        
        # 确认运行
        if not confirm_run(config):
            print("运行已取消")
            return
        
        # 运行插值
        success = run_with_progress(config)
        
        if success:
            print("\n程序运行成功完成!")
            input("按回车键退出...")
        else:
            print("\n程序运行失败!")
            input("按回车键退出...")
            
    except KeyboardInterrupt:
        print("\n\n用户中断程序运行")
    except Exception as e:
        print(f"\n程序异常: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
