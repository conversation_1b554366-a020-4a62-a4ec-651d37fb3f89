# -*- coding: utf-8 -*-
"""
测试README.md中的命令是否正确工作
"""

import os
import sys
import subprocess

def test_command(command, description):
    """测试单个命令"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        # 只检查命令是否能正确解析，不实际运行
        if command.startswith("python"):
            script_name = command.split()[1]
            if os.path.exists(script_name):
                print(f"✅ 脚本文件存在: {script_name}")
                
                # 检查脚本是否有语法错误
                result = subprocess.run([sys.executable, "-m", "py_compile", script_name], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ 脚本语法正确")
                else:
                    print(f"❌ 脚本语法错误: {result.stderr}")
                    return False
            else:
                print(f"❌ 脚本文件不存在: {script_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 测试README.md中的命令")
    
    # README.md中提到的命令
    commands = [
        ("python run_interpolation.py", "交互式运行所有插值方法"),
        ("python main_controller.py --methods OI --generate_grids", "运行OI方法并生成栅格"),
        ("python main_controller.py --methods all --num_processes 4", "运行所有方法，4个进程"),
        ("python main_controller.py --methods PRISM Kriging --events 2020-1 2020-2", "运行指定方法和事件"),
        ("python run_oi_optimized.py", "运行优化的OI插值"),
    ]
    
    success_count = 0
    total_count = len(commands)
    
    for command, description in commands:
        if test_command(command, description):
            success_count += 1
    
    # 测试结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print(f"{'='*60}")
    print(f"总命令数: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有命令测试通过！")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个命令需要修复")
    
    # 检查必要文件
    print(f"\n📁 检查必要文件:")
    required_files = [
        "config.py",
        "utils.py", 
        "main_controller.py",
        "run_interpolation.py",
        "stations.csv",
        "terrain/dem.asc",
        "IDW_python/idw_interpolation.py",
        "Kriging_python/kriging_interpolation.py",
        "OI_python/oi_interpolation.py",
        "PRISM_python/prism_interpolation.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {len(missing_files)} 个")
        for file in missing_files:
            print(f"   - {file}")
    else:
        print(f"\n✅ 所有必要文件都存在")
    
    # 检查配置
    print(f"\n⚙️  检查配置:")
    try:
        from config import InterpolationConfig
        config = InterpolationConfig()
        
        print(f"✅ 配置加载成功")
        print(f"   插值方法: {config.methods}")
        print(f"   洪水事件: {len(config.flood_events)} 个")
        print(f"   点插值进程: {config.num_processes}")
        print(f"   栅格插值进程: {config.grid_parallel_chunks}")
        print(f"   生成栅格: {config.generate_grids}")
        
        # 验证路径
        paths = config.validate_paths()
        missing_paths = [path for path, exists in paths.items() if not exists]
        
        if missing_paths:
            print(f"⚠️  路径问题: {len(missing_paths)} 个")
            for path in missing_paths[:3]:  # 只显示前3个
                print(f"   - {path}")
            if len(missing_paths) > 3:
                print(f"   ... 还有 {len(missing_paths)-3} 个")
        else:
            print(f"✅ 所有路径验证通过")
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
    
    return success_count == total_count and len(missing_files) == 0

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 README.md命令测试全部通过！")
        print(f"💡 您可以按照README.md中的说明使用系统")
    else:
        print(f"\n⚠️  发现问题，需要修复后才能正常使用")
        print(f"💡 请检查上述错误信息并修复相应问题")
